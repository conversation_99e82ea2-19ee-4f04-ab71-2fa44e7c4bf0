<!doctype html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="designer" content="imgix">
	<meta name="developer" content="imgix">
	<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">

	<title>Luminous Playground</title>
	<link rel="stylesheet" media="screen, projection" href="./dist/luminous-basic.css">
	<style type="text/css">
		body {
			text-align: center;
			font-family: Helvetica Neue, Arial, sans;
			margin-top: 2em;
			background: #FAFAFA;
		}

		section {
			max-width: 32em;
			margin: 0 auto;
		}

		h1 {
			text-align: center;
			color: #013C4A;
			margin-top: 3em;
			margin-bottom: 1em;
		}

		p {
			max-width: 32em;
			margin-bottom: 1em;
			text-align: left;
			color: #23637f;
			line-height: 1.6em;
		}

		p:last-of-type {
			margin-bottom: 2em;
		}

		a {
			color: #00C0FA;
		}

		.zoom-in {
			cursor: zoom-in;
		}

		a img {
			max-width: 100%;
		}

		.gallery-demo-wrapper {
			list-style: none;
			text-align: center;
			padding-left: 0;
			margin-bottom: 36px;
		}

		.gallery-demo-wrapper li {
			display: inline-block;
			padding: 0 6px;
		}
	</style>
</head>

<body>
	<a class="simple-demo zoom-in" href="https://assets.imgix.net/unsplash/pretty2.jpg?w=1600">
		<img src="https://assets.imgix.net/unsplash/pretty2.jpg?w=400" alt="Mountainous landscape">
	</a>

	<section>
		<h1>Luminous Demo</h1>
		<p>This is a demo of Luminous, a simple, lightweight, no-dependencies JavaScript image lightbox from
			<a href="https://imgix.com">imgix</a>. This demo uses the simple included theme, but it's very easy to extend and customize to fit your needs. You
			can
			<a href="https://github.com/imgix/luminous">learn more and download it here</a>.</p>
	</section>

	<ul class="gallery-demo-wrapper">
		<li>
			<a class="gallery-demo" href="https://assets.imgix.net/unsplash/coyote.jpg?w=1600">
				<img src="https://assets.imgix.net/unsplash/coyote.jpg?w=100" alt="Coyote">
			</a>
		</li>
		<li>
			<a class="gallery-demo" href="https://assets.imgix.net/unsplash/motorbike.jpg?w=1600">
				<img src="https://assets.imgix.net/unsplash/motorbike.jpg?w=100" alt="Motorbike">
			</a>
		</li>
		<li>
			<a class="gallery-demo" href="https://assets.imgix.net/unsplash/hotairballoon.jpg?w=1600">
				<img src="https://assets.imgix.net/unsplash/hotairballoon.jpg?w=100" alt="Hot air balloon">
			</a>
		</li>
	</ul>

	<a href="https://imgix.com">
		<img src="https://assets.imgix.net/presskit/imgix-presskit.pdf?page=3&amp;fm=png&amp;w=120" srcset="
          https://assets.imgix.net/presskit/imgix-presskit.pdf?page=3&amp;fm=png&amp;w=120 1x,
          https://assets.imgix.net/presskit/imgix-presskit.pdf?page=3&amp;fm=png&amp;w=120&amp;dpr=2 2x,
          https://assets.imgix.net/presskit/imgix-presskit.pdf?page=3&amp;fm=png&amp;w=120&amp;dpr=3 3x,
        " alt="imgix">
	</a>

	<script src="./dist/Luminous.js"></script>
	<script>
		new Luminous(document.querySelector('.simple-demo'));
		new LuminousGallery(document.querySelectorAll('.gallery-demo'), {}, {
			caption: function (trigger) {
				return trigger.querySelector('img').getAttribute('alt');
			}
		});
	</script>
</body>

</html>
