/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */

.wp-block-cgb-block-easy-image-gallery-block  {
  background: $green;
  border: 0.2rem solid $black;
  color: $black;
  margin: 0 auto;
  max-width: 740px;
  padding: 2rem;
}
.image-gallery {
  display: flex;
  flex-wrap: wrap;  
}
img.gallery-item {
  object-fit: cover;
  width: 150px;
  height: 150px; 
}
.gallery-item-container {
  display: inline-block; 
  flex: 0 0 33.33%;
}
@media (max-width: 600px) {
  .gallery-item-container {
    flex: 0 0 50%;
  }
}
@media (max-width: 400px) {
  .image-gallery {
    justify-content: center;
  }
  .gallery-item-container {
    flex: 0 0 100%;
  }
}