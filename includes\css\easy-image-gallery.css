@font-face {
  font-family: 'easy-image-gallery';
  src: url('../fonts/easy-image-gallery.eot');
  src: url('../fonts/easy-image-gallery.eot?#iefix') format('embedded-opentype'), url('../fonts/easy-image-gallery.woff') format('woff'), url('../fonts/easy-image-gallery.ttf') format('truetype'), url('../fonts/easy-image-gallery.svg#easy-image-gallery') format('svg');
  font-weight: normal;
  font-style: normal;
}
.eig-popup {
  display: block;
}
.eig-popup .icon-view:before {
  content: "\e005";
}
.eig-popup .icon-view {
  font-family: 'easy-image-gallery';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  font-size: 30px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  color: #fff;
  opacity: 0;
  visibility: visible;
  -webkit-transition: opacity 0.3s linear;
  -moz-transition: opacity 0.3s linear;
  -ms-transition: opacity 0.3s linear;
  -o-transition: opacity 0.3s linear;
  transition: opacity 0.3s linear;
  z-index: 1;
}
.eig-popup .overlay {
  visibility: hidden;
  display: block;
  height: 0%;
  width: 0%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  opacity: 0.4;
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s linear;
  -ms-transition: all 0.3s linear;
  -o-transition: all 0.3s linear;
  transition: all 0.3s linear;
}
.eig-popup:hover .overlay {
  visibility: visible;
  background: #000;
  height: 100%;
  width: 100%;
}
.popup:hover img {

}
.eig-popup:hover .icon-view {
  opacity: 0.8;
}
.easy-image-gallery{
  margin: 0 auto;
  padding: 0;
}
.easy-image-gallery li {
  display: inline-block;
  padding: 0 20px 20px 0;
  margin: 0;
}
.easy-image-gallery a {
  position: relative;
  display: block;
}
.easy-image-gallery a img {
  display: block;
  border-top-right-radius: 20px;
  -webkit-border-top-right-radius: 20px;
  -moz-border-top-right-radius: 20px;
  -ms-border-top-right-radius: 20px;
  -o-border-top-right-radius: 20px;
  transition: .3s all;
  -webkit-transition: .3s all;
  -moz-transition: .3s all;
  -ms-transition: .3s all;
  -o-transition: .3s all;
}
.lum-lightbox.lum-open {
	background-color: rgba(0,0,0,.8);
	z-index: 9999;
}
.lum-lightbox-inner img {
  margin: 0 auto;
}
#fancybox-title{
  left: initial !important;
}
#fancybox-title-float-wrap td{
  background: transparent;
  background: #000;
  max-width: 50%;
  white-space: initial !important;
}

@media (min-width: 600px) {
  .admin-bar .fancybox-container {
    margin-top: 46px;
  }
  .admin-bar .fancybox-caption {
    bottom: 46px;
  }
}


@media (min-width: 783px) {
  .admin-bar .fancybox-container {
    margin-top: 32px;
  }  

  .admin-bar .fancybox-caption {
    bottom: 32px;
  }
}
