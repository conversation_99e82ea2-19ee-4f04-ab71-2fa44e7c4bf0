!function(){function e(t,i,n){function s(l,r){if(!i[l]){if(!t[l]){var a="function"==typeof require&&require;if(!r&&a)return a(l,!0);if(o)return o(l,!0);var u=new Error("Cannot find module '"+l+"'");throw u.code="MODULE_NOT_FOUND",u}var d=i[l]={exports:{}};t[l][0].call(d.exports,function(e){var i=t[l][1][e];return s(i||e)},d,d.exports,e,t,i,n)}return i[l].exports}for(var o="function"==typeof require&&require,l=0;l<n.length;l++)s(n[l]);return s}return e}()({1:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};Object.defineProperty(i,"__esModule",{value:!0});var l="function"==typeof Symbol&&"symbol"===o(Symbol.iterator)?function(e){return"undefined"==typeof e?"undefined":o(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":"undefined"==typeof e?"undefined":o(e)},r=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),a=e("./util/dom"),u=e("./util/throwIfMissing"),d=n(u),c=37,h=39,g="undefined"!=typeof document&&"animation"in document.createElement("div").style,p=function(){function e(){var t=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};s(this,e),this._sizeImgWrapperEl=function(){var e=t.imgWrapperEl.style;e.width=t.innerEl.clientWidth+"px",e.maxWidth=t.innerEl.clientWidth+"px",e.height=t.innerEl.clientHeight-t.captionEl.clientHeight+"px",e.maxHeight=t.innerEl.clientHeight-t.captionEl.clientHeight+"px"},this._handleKeydown=function(e){e.keyCode==c?t.showPrevious():e.keyCode==h&&t.showNext()},this.showNext=function(){t.settings._gallery&&(t.currentTrigger=t.settings._gallery.nextTrigger(t.currentTrigger),t._updateImgSrc(),t._updateCaption(),t._sizeImgWrapperEl())},this.showPrevious=function(){t.settings._gallery&&(t.currentTrigger=t.settings._gallery.previousTrigger(t.currentTrigger),t._updateImgSrc(),t._updateCaption(),t._sizeImgWrapperEl())},this._completeOpen=function(){t.el.removeEventListener("animationend",t._completeOpen,!1),(0,a.removeClasses)(t.el,t.openingClasses)},this._completeClose=function(){t.el.removeEventListener("animationend",t._completeClose,!1),(0,a.removeClasses)(t.el,t.openClasses),(0,a.removeClasses)(t.el,t.closingClasses)};var n=i.namespace,o=void 0===n?null:n,l=i.parentEl,r=void 0===l?(0,d["default"])():l,u=i.triggerEl,g=void 0===u?(0,d["default"])():u,p=i.sourceAttribute,f=void 0===p?(0,d["default"])():p,m=i.caption,v=void 0===m?null:m,y=i.includeImgixJSClass,b=void 0!==y&&y,E=i._gallery,_=void 0===E?null:E,C=i._arrowNavigation,w=void 0===C?null:C;if(this.settings={namespace:o,parentEl:r,triggerEl:g,sourceAttribute:f,caption:v,includeImgixJSClass:b,_gallery:_,_arrowNavigation:w},!(0,a.isDOMElement)(this.settings.parentEl))throw new TypeError("`new Lightbox` requires a DOM element passed as `parentEl`.");this.currentTrigger=this.settings.triggerEl,this.openClasses=this._buildClasses("open"),this.openingClasses=this._buildClasses("opening"),this.closingClasses=this._buildClasses("closing"),this.hasBeenLoaded=!1,this.elementBuilt=!1}return r(e,[{key:"_buildClasses",value:function(e){var t=["lum-"+e],i=this.settings.namespace;return i&&t.push(i+"-"+e),t}},{key:"_buildElement",value:function(){this.el=document.createElement("div"),(0,a.addClasses)(this.el,this._buildClasses("lightbox")),this.innerEl=document.createElement("div"),(0,a.addClasses)(this.innerEl,this._buildClasses("lightbox-inner")),this.el.appendChild(this.innerEl);var e=document.createElement("div");(0,a.addClasses)(e,this._buildClasses("lightbox-loader")),this.innerEl.appendChild(e),this.imgWrapperEl=document.createElement("div"),(0,a.addClasses)(this.imgWrapperEl,this._buildClasses("lightbox-image-wrapper")),this.innerEl.appendChild(this.imgWrapperEl);var t=document.createElement("span");(0,a.addClasses)(t,this._buildClasses("lightbox-position-helper")),this.imgWrapperEl.appendChild(t),this.imgEl=document.createElement("img"),(0,a.addClasses)(this.imgEl,this._buildClasses("img")),t.appendChild(this.imgEl),this.captionEl=document.createElement("p"),(0,a.addClasses)(this.captionEl,this._buildClasses("lightbox-caption")),t.appendChild(this.captionEl),this.settings._gallery&&this._setUpGalleryElements(),this.settings.parentEl.appendChild(this.el),this._updateImgSrc(),this._updateCaption(),this.settings.includeImgixJSClass&&this.imgEl.classList.add("imgix-fluid")}},{key:"_setUpGalleryElements",value:function(){this._buildGalleryButton("previous",this.showPrevious),this._buildGalleryButton("next",this.showNext)}},{key:"_buildGalleryButton",value:function(e,t){var i=document.createElement("button");this[e+"Button"]=i,i.innerText=e,(0,a.addClasses)(i,this._buildClasses(e+"-button")),(0,a.addClasses)(i,this._buildClasses("gallery-button")),this.innerEl.appendChild(i),i.addEventListener("click",function(e){e.stopPropagation(),t()},!1)}},{key:"_updateCaption",value:function(){var e=l(this.settings.caption),t="";"string"===e?t=this.settings.caption:"function"===e&&(t=this.settings.caption(this.currentTrigger)),this.captionEl.innerHTML=t}},{key:"_updateImgSrc",value:function(){var e=this,t=this.currentTrigger.getAttribute(this.settings.sourceAttribute);if(!t)throw new Error("No image URL was found in the "+this.settings.sourceAttribute+" attribute of the trigger.");var i=this._buildClasses("loading");this.hasBeenLoaded||(0,a.addClasses)(this.el,i),this.imgEl.onload=function(){(0,a.removeClasses)(e.el,i),e.hasBeenLoaded=!0},this.imgEl.setAttribute("src",t)}},{key:"open",value:function(){this.elementBuilt||(this._buildElement(),this.elementBuilt=!0),this.currentTrigger=this.settings.triggerEl,this._updateImgSrc(),this._updateCaption(),(0,a.addClasses)(this.el,this.openClasses),this._sizeImgWrapperEl(),window.addEventListener("resize",this._sizeImgWrapperEl,!1),this.settings._arrowNavigation&&window.addEventListener("keydown",this._handleKeydown,!1),g&&(this.el.addEventListener("animationend",this._completeOpen,!1),(0,a.addClasses)(this.el,this.openingClasses))}},{key:"close",value:function(){window.removeEventListener("resize",this._sizeImgWrapperEl,!1),this.settings._arrowNavigation&&window.removeEventListener("keydown",this._handleKeydown,!1),g?(this.el.addEventListener("animationend",this._completeClose,!1),(0,a.addClasses)(this.el,this.closingClasses)):(0,a.removeClasses)(this.el,this.openClasses)}},{key:"destroy",value:function(){this.el&&this.settings.parentEl.removeChild(this.el)}}]),e}();i["default"]=p},{"./util/dom":6,"./util/throwIfMissing":7}],2:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o,l,r,a=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),u=e("./util/dom"),d=e("./injectBaseStylesheet"),c=n(d),h=e("./Lightbox"),g=n(h);t.exports=(l=o=function(){function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(s(this,e),r.call(this),this.isOpen=!1,this.trigger=t,!(0,u.isDOMElement)(this.trigger))throw new TypeError("`new Luminous` requires a DOM element as its first argument.");var n=i.namespace,o=void 0===n?null:n,l=i.sourceAttribute,a=void 0===l?"href":l,d=i.caption,h=void 0===d?null:d,g=i.openTrigger,p=void 0===g?"click":g,f=i.closeTrigger,m=void 0===f?"click":f,v=i.closeWithEscape,y=void 0===v||v,b=i.closeOnScroll,E=void 0!==b&&b,_=i.appendToSelector,C=void 0===_?"body":_,w=i.onOpen,x=void 0===w?null:w,S=i.onClose,L=void 0===S?null:S,O=i.includeImgixJSClass,T=void 0!==O&&O,k=i.injectBaseStyles,I=void 0===k||k,M=i._gallery,N=void 0===M?null:M,W=i._arrowNavigation,B=void 0===W?null:W;this.settings={namespace:o,sourceAttribute:a,caption:h,openTrigger:p,closeTrigger:m,closeWithEscape:y,closeOnScroll:E,appendToSelector:C,onOpen:x,onClose:L,includeImgixJSClass:T,injectBaseStyles:I,_gallery:N,_arrowNavigation:B},this.settings.injectBaseStyles&&(0,c["default"])(),this._buildLightbox(),this._bindEvents()}return a(e,[{key:"_buildLightbox",value:function(){this.lightbox=new g["default"]({namespace:this.settings.namespace,parentEl:document.querySelector(this.settings.appendToSelector),triggerEl:this.trigger,sourceAttribute:this.settings.sourceAttribute,caption:this.settings.caption,includeImgixJSClass:this.settings.includeImgixJSClass,_gallery:this.settings._gallery,_arrowNavigation:this.settings._arrowNavigation})}},{key:"_bindEvents",value:function(){this.trigger.addEventListener(this.settings.openTrigger,this.open,!1),this.settings.closeWithEscape&&window.addEventListener("keyup",this._handleKeyup,!1)}},{key:"_bindCloseEvent",value:function(){this.lightbox.el.addEventListener(this.settings.closeTrigger,this.close,!1)}},{key:"_unbindEvents",value:function(){this.trigger.removeEventListener(this.settings.openTrigger,this.open,!1),this.lightbox.el&&this.lightbox.el.removeEventListener(this.settings.closeTrigger,this.close,!1),this.settings.closeWithEscape&&window.removeEventListener("keyup",this._handleKeyup,!1)}}]),e}(),r=function(){var e=this;this.VERSION="2.0.0",this.open=function(t){t&&"function"==typeof t.preventDefault&&t.preventDefault();var i=e.lightbox.elementBuilt;e.lightbox.open(),i||e._bindCloseEvent(),e.settings.closeOnScroll&&window.addEventListener("scroll",e.close,!1);var n=e.settings.onOpen;n&&"function"==typeof n&&n(),e.isOpen=!0},this.close=function(t){t&&"function"==typeof t.preventDefault&&t.preventDefault(),e.settings.closeOnScroll&&window.removeEventListener("scroll",e.close,!1),e.lightbox.close();var i=e.settings.onClose;i&&"function"==typeof i&&i(),e.isOpen=!1},this._handleKeyup=function(t){e.isOpen&&27===t.keyCode&&e.close()},this.destroy=function(){e._unbindEvents(),e.lightbox.destroy()}},l)},{"./Lightbox":1,"./injectBaseStylesheet":4,"./util/dom":6}],3:[function(e,t,i){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(i,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),t}}(),l=(e("./util/dom"),e("./Luminous")),r=n(l),a=function(){function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};s(this,e),this.boundMethod=function(){};var o=i.arrowNavigation,l=void 0===o||o;this.settings={arrowNavigation:l},this.triggers=t,this.luminousOpts=n,this.luminousOpts._gallery=this,this.luminousOpts._arrowNavigation=this.settings.arrowNavigation,this._constructLuminousInstances()}return o(e,[{key:"_constructLuminousInstances",value:function(){this.luminousInstances=[];for(var e=this.triggers.length,t=0;t<e;t++){var i=this.triggers[t],n=new r["default"](i,this.luminousOpts);this.luminousInstances.push(n)}}},{key:"nextTrigger",value:function(e){var t=Array.prototype.indexOf.call(this.triggers,e)+1;return t>=this.triggers.length?this.triggers[0]:this.triggers[t]}},{key:"previousTrigger",value:function(e){var t=Array.prototype.indexOf.call(this.triggers,e)-1;return t<0?this.triggers[this.triggers.length-1]:this.triggers[t]}},{key:"destroy",value:function(){}}]),e}();i["default"]=a},{"./Luminous":2,"./util/dom":6}],4:[function(e,t,i){"use strict";function n(){if(!document.querySelector(".lum-base-styles")){var e=document.createElement("style");e.type="text/css",e.classList.add("lum-base-styles"),e.appendChild(document.createTextNode(s));var t=document.head;t.insertBefore(e,t.firstChild)}}Object.defineProperty(i,"__esModule",{value:!0}),i["default"]=n;var s="\n@keyframes lum-noop {\n  0% { zoom: 1; }\n}\n\n.lum-lightbox {\n  position: fixed;\n  display: none;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n\n.lum-lightbox.lum-open {\n  display: block;\n}\n\n.lum-lightbox.lum-opening, .lum-lightbox.lum-closing {\n  animation: lum-noop 1ms;\n}\n\n.lum-lightbox-inner {\n  position: absolute;\n  top: 0%;\n  right: 0%;\n  bottom: 0%;\n  left: 0%;\n\n  overflow: hidden;\n}\n\n.lum-lightbox-loader {\n  display: none;\n}\n\n.lum-lightbox-inner img {\n  max-width: 100%;\n  max-height: 100%;\n}\n\n.lum-lightbox-image-wrapper {\n  vertical-align: middle;\n  display: table-cell;\n  text-align: center;\n}\n"},{}],5:[function(e,t,i){(function(t){"use strict";function i(e){return e&&e.__esModule?e:{"default":e}}var n=e("./Luminous"),s=i(n),o=e("./LuminousGallery"),l=i(o);t.Luminous=s["default"],t.LuminousGallery=l["default"]}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./Luminous":2,"./LuminousGallery":3}],6:[function(e,t,i){"use strict";function n(e){return a?e instanceof HTMLElement:e&&"object"===("undefined"==typeof e?"undefined":r(e))&&null!==e&&1===e.nodeType&&"string"==typeof e.nodeName}function s(e,t){t.forEach(function(t){e.classList.add(t)})}function o(e,t){t.forEach(function(t){e.classList.remove(t)})}var l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};Object.defineProperty(i,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"===l(Symbol.iterator)?function(e){return"undefined"==typeof e?"undefined":l(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":"undefined"==typeof e?"undefined":l(e)};i.isDOMElement=n,i.addClasses=s,i.removeClasses=o;var a="object"===("undefined"==typeof HTMLElement?"undefined":r(HTMLElement))},{}],7:[function(e,t,i){"use strict";function n(){throw new Error("Missing parameter")}Object.defineProperty(i,"__esModule",{value:!0}),i["default"]=n},{}]},{},[5]);