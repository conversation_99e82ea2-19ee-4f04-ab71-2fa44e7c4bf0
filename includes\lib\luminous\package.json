{"name": "luminous-lightbox", "version": "2.0.0", "description": "A simple, lightweight, no-dependencies JavaScript image lightbox.", "main": "lib/lum-commonjs.js", "scripts": {"format": "prettier --write \"{src,test}/**/*.js\"", "format:check": "prettier --list-different \"{src,test}/**/*.js\"", "test": "gulp build-ci", "start": "gulp"}, "repository": {"type": "git", "url": "git+https://github.com/imgix/luminous.git"}, "keywords": ["javascript", "lightbox"], "author": "imgix", "license": "MIT", "bugs": {"url": "https://github.com/imgix/luminous/issues"}, "homepage": "https://github.com/imgix/luminous#readme", "dependencies": {"babelify": "^7.2.0", "babel-preset-es2015": "^6.3.13", "babel-preset-stage-0": "^6.3.13"}, "devDependencies": {"babel": "^6.3.13", "babel-core": "^6.3.21", "browserify": "^12.0.1", "gulp": "^3.9.0", "gulp-autoprefixer": "^5.0.0", "gulp-babel": "^6.1.3", "gulp-concat": "^2.6.0", "gulp-cssnano": "^2.0.0", "gulp-rename": "^1.2.2", "gulp-uglify": "^1.5.1", "gulp-util": "^3.0.7", "jasmine-core": "^2.4.1", "karma": "^0.13.15", "karma-babel-preprocessor": "^6.0.1", "karma-browserify": "^4.4.2", "karma-firefox-launcher": "^0.1.7", "karma-jasmine": "^0.3.6", "karma-phantomjs-launcher": "^1.0.2", "karma-sauce-launcher": "^0.3.0", "phantomjs": "^1.9.19", "prettier": "^1.13.4", "run-sequence": "^1.1.5", "vinyl-buffer": "^1.0.0", "vinyl-source-stream": "^1.1.0"}, "browserify": {"transform": ["babe<PERSON>"]}}